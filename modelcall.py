import enum

from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from pydantic import BaseModel

from langchain.prompts import ChatPromptTemplate
from modelconfig import model_name,base_url,api_key
from langchain_first_tools import operator_tools
# 构建输出解析器并获取解析格式

class Book(BaseModel):
    operation:list
    old: list
    new: list
    pro: list


# 初始化时传入Pydantic模型
json_parser = JsonOutputParser(pydantic_object=Book)

# 构建提示词模版
prompt1 = PromptTemplate(
    template ="""
你是通信网络配置需求分析助手，可以从用户意图描述中一句话简单表述需求，还能提取配置相关关键字，可能是隐含信息，输出操作类型列表operation，枚举包含["新增","修改","查询"]，可能含国歌,以及关键字列表，包括old(已存在IP端口网元等)，new(新增IP端口网元等)，pro(其他专业名词)三个list。\n{format_instructions}\n
输出严格按照格式，不需要注释。
以下是用户描述：{query}
""",
    input_variables = ["query"],
    partial_variables={"format_instructions": json_parser},
)

model = ChatOpenAI(model=model_name,
                   openai_api_key=api_key,
                   openai_api_base=base_url)
llmtools = model.bind_tools(operator_tools)

def print_message(_):
    # print("你好我是网络配置需求助手，请问你有什么问题？")
    # query = input("输入：")
    query = "因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，申请资源池承载B网MVSP_NMS接收地址需求如下： 原接收地址范围由************/28调整为***********/27（河南）；新增接收地址范围***********/24（黑龙江）；"
    print("用户输入：",query)
    return {"query":query}

chain = print_message| prompt1 | model |json_parser

result = chain.invoke({})
print(result)