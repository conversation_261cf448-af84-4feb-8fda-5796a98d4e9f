"""
简单的API测试脚本
"""

import sys
import os
import asyncio
import json

# 添加项目路径到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_api_directly():
    """直接测试API功能"""
    try:
        print("测试API功能...")
        
        # 导入必要的模块
        from app.services.config_generation import config_generation_service
        from app.models.request import ConfigGenerationRequest
        
        # 测试通用能力
        print("\n1. 测试通用能力...")
        request = ConfigGenerationRequest(
            user_input="请配置华为路由器的BGP协议",
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        # 模拟LLM响应
        print("模拟配置生成...")
        print("✓ 通用能力测试准备完成")
        
        # 测试定制化能力
        print("\n2. 测试定制化能力...")
        request2 = ConfigGenerationRequest(
            user_input="""CE端BGP AS号|CE端设备|设备型号|CE端口|CE端互联IP地址
65001|Router1|AR2220|GE0/0/1|***********
65002|Router2|AR2220|GE0/0/2|***********""",
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        print("✓ 定制化能力测试准备完成")
        
        # 测试意图识别
        print("\n3. 测试意图识别...")
        from app.services.intent_recognition import intent_service
        from app.models.request import IntentRecognitionRequest
        
        intent_request = IntentRecognitionRequest(
            user_input="CE端BGP AS号|CE端设备|设备型号"
        )
        
        result = await intent_service.recognize_intent(intent_request)
        print(f"意图识别结果: {result.intent_type}")
        print(f"置信度: {result.confidence}")
        
        print("\n✓ 所有API功能测试完成！")
        return True
        
    except Exception as e:
        print(f"✗ API测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_fastapi_app():
    """测试FastAPI应用创建"""
    try:
        print("\n测试FastAPI应用...")
        
        from app.main import app
        
        print(f"应用标题: {app.title}")
        print(f"应用版本: {app.version}")
        print("✓ FastAPI应用创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ FastAPI应用测试失败: {str(e)}")
        return False

def test_template_rendering():
    """测试模板渲染"""
    try:
        print("\n测试模板渲染...")
        
        from app.core.customized_capability import customized_capability
        from app.models.request import CustomizedCapabilityRequest
        
        request = CustomizedCapabilityRequest(
            excel_content="""CE端BGP AS号|CE端设备|设备型号
65001|Router1|AR2220""",
            device_type="router",
            vendor="huawei"
        )
        
        # 测试数据解析
        data_items = customized_capability._parse_excel_content(request.excel_content)
        print(f"解析数据项: {len(data_items)}")
        
        # 测试模板名称获取
        template_name = customized_capability._get_template_name("router", "huawei")
        print(f"模板名称: {template_name}")
        
        print("✓ 模板渲染测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 模板渲染测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("开始API功能测试...\n")
    
    tests = [
        ("FastAPI应用测试", test_fastapi_app),
        ("模板渲染测试", test_template_rendering),
        ("API功能测试", test_api_directly),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}执行异常: {str(e)}")
    
    print(f"\n测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有API测试通过！服务功能正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    asyncio.run(main())
