[project]
name = "config-agent-py"
version = "0.1.0"
description = "数通设备配置生成智能体服务"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "httpx>=0.25.2",
    "jinja2>=3.1.2",
    "pandas>=2.1.4",
    "openpyxl>=3.1.2",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.1",
    "loguru>=0.7.2",
    "python-dotenv>=1.0.0",
    "tenacity>=8.2.3",
    "asyncio-throttle>=1.0.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
