"""
意图识别服务
"""

import re
from typing import Dict, Any, Optional, Tuple
from loguru import logger

from app.config.constants import (
    IntentType, DeviceType, VendorType, 
    CUSTOMIZED_TABLE_HEADERS, INVALID_QUESTION_RESPONSE
)
from app.models.request import IntentRecognitionRequest
from app.models.response import IntentRecognitionResponse
from app.utils.text_converter import text_converter


class IntentRecognitionService:
    """意图识别服务类"""
    
    def __init__(self):
        self.device_keywords = {
            DeviceType.ROUTER: ["路由器", "router", "路由", "R1", "R2", "AR", "NE"],
            DeviceType.SWITCH: ["交换机", "switch", "交换", "S1", "S2", "CE", "SW"],
            DeviceType.FIREWALL: ["防火墙", "firewall", "防火", "FW", "USG", "ASA"]
        }
        
        self.vendor_keywords = {
            VendorType.HUAWEI: ["华为", "huawei", "hw", "华三", "h3c"],
            VendorType.CISCO: ["思科", "cisco", "ios"],
            VendorType.H3C: ["华三", "h3c", "comware"],
            VendorType.JUNIPER: ["瞻博", "juniper", "junos"]
        }
    
    def _extract_device_and_vendor(self, text: str) -> Tuple[Optional[str], Optional[str]]:
        """从文本中提取设备类型和厂商信息"""
        text_lower = text.lower()
        
        # 提取设备类型
        device_type = None
        for device, keywords in self.device_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    device_type = device.value
                    break
            if device_type:
                break
        
        # 提取厂商信息
        vendor = None
        for vendor_enum, keywords in self.vendor_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    vendor = vendor_enum.value
                    break
            if vendor:
                break
        
        return device_type, vendor
    
    def _is_config_related(self, text: str) -> bool:
        """判断问题是否与设备配置相关"""
        config_keywords = [
            "配置", "config", "设置", "setup", "命令", "command",
            "接口", "interface", "路由", "route", "vlan", "bgp",
            "ospf", "isis", "静态路由", "动态路由", "交换", "switch",
            "端口", "port", "ip", "地址", "address", "网络", "network",
            "协议", "protocol", "策略", "policy", "acl", "nat",
            "vpn", "tunnel", "隧道", "安全", "security"
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in config_keywords)
    
    def _detect_customized_intent(self, text: str) -> bool:
        """检测是否为定制化能力意图"""
        # 检查是否包含定制化表头关键词
        text_lower = text.lower()
        
        # 计算匹配的表头数量
        matched_headers = 0
        for header in CUSTOMIZED_TABLE_HEADERS:
            if header.lower() in text_lower:
                matched_headers += 1
        
        # 如果匹配的表头超过阈值，认为是定制化意图
        threshold = max(3, len(CUSTOMIZED_TABLE_HEADERS) * 0.2)  # 至少3个或20%
        
        logger.info(f"定制化表头匹配数量: {matched_headers}, 阈值: {threshold}")
        return matched_headers >= threshold
    
    def _detect_open_intent(self, text: str) -> bool:
        """检测是否为开放式能力意图"""
        # 检查是否包含表格和配置相关内容，但不是定制化表头
        has_table = "|" in text or "表格" in text or "excel" in text.lower()
        has_config = "配置" in text or "config" in text.lower() or "txt" in text.lower()
        has_example = any(keyword in text.lower() for keyword in ["示例", "例子", "参考", "模板", "fewshot"])
        
        # 不是定制化意图，但包含表格和配置信息
        is_not_customized = not self._detect_customized_intent(text)
        
        return has_table and has_config and is_not_customized and has_example
    
    def _detect_general_intent(self, text: str) -> bool:
        """检测是否为通用能力意图"""
        # 纯文字问题，没有表格内容
        has_no_table = "|" not in text and "表格" not in text and "excel" not in text.lower()
        is_config_related = self._is_config_related(text)
        
        return has_no_table and is_config_related
    
    def _extract_table_info(self, text: str) -> Dict[str, Any]:
        """提取表格信息"""
        table_info = text_converter.extract_table_info(text)
        
        # 检查是否包含定制化表头
        customized_headers = []
        if table_info["has_table"]:
            for header in table_info["headers"]:
                if header in CUSTOMIZED_TABLE_HEADERS:
                    customized_headers.append(header)
        
        table_info["customized_headers"] = customized_headers
        return table_info
    
    async def recognize_intent(self, request: IntentRecognitionRequest) -> IntentRecognitionResponse:
        """识别用户意图"""
        try:
            user_input = request.user_input.strip()
            
            # 检查是否与配置相关
            if not self._is_config_related(user_input):
                return IntentRecognitionResponse(
                    success=False,
                    message=INVALID_QUESTION_RESPONSE,
                    code=400,
                    intent_type=IntentType.INVALID.value,
                    confidence=1.0
                )
            
            # 提取设备类型和厂商
            device_type, vendor = self._extract_device_and_vendor(user_input)
            
            # 提取表格信息
            table_info = self._extract_table_info(user_input)
            
            # 意图识别逻辑
            intent_type = IntentType.GENERAL.value  # 默认为通用能力
            confidence = 0.5
            
            if self._detect_customized_intent(user_input):
                intent_type = IntentType.CUSTOMIZED.value
                confidence = 0.9
                logger.info("识别为定制化能力意图")
            elif self._detect_open_intent(user_input):
                intent_type = IntentType.OPEN.value
                confidence = 0.8
                logger.info("识别为开放式能力意图")
            elif self._detect_general_intent(user_input):
                intent_type = IntentType.GENERAL.value
                confidence = 0.7
                logger.info("识别为通用能力意图")
            
            # 构建提取的信息
            extracted_info = {
                "table_info": table_info,
                "text_length": len(user_input),
                "has_device_info": bool(device_type or vendor)
            }
            
            return IntentRecognitionResponse(
                success=True,
                message="意图识别成功",
                code=200,
                intent_type=intent_type,
                confidence=confidence,
                device_type=device_type,
                vendor=vendor,
                extracted_info=extracted_info
            )
            
        except Exception as e:
            logger.error(f"意图识别失败: {str(e)}")
            return IntentRecognitionResponse(
                success=False,
                message=f"意图识别失败: {str(e)}",
                code=500,
                intent_type=IntentType.INVALID.value,
                confidence=0.0
            )


# 全局意图识别服务实例
intent_service = IntentRecognitionService()
