"""
文本转换工具
"""

import io
import re
from typing import Dict, Any, Optional
import pandas as pd
from loguru import logger

from app.config.constants import SUPPORTED_FILE_TYPES


class TextConverter:
    """文本转换器类"""
    
    @staticmethod
    def excel_to_text(file_content: bytes, file_name: str) -> str:
        """将Excel文件转换为文本"""
        try:
            # 使用pandas读取Excel文件
            excel_file = io.BytesIO(file_content)
            
            # 尝试读取所有工作表
            excel_data = pd.read_excel(excel_file, sheet_name=None)
            
            text_content = []
            
            for sheet_name, df in excel_data.items():
                text_content.append(f"工作表: {sheet_name}")
                text_content.append("=" * 50)
                
                # 将DataFrame转换为文本格式
                if not df.empty:
                    # 使用管道符分隔列
                    header = "|".join(str(col) for col in df.columns)
                    text_content.append(header)
                    
                    for _, row in df.iterrows():
                        row_text = "|".join(str(val) if pd.notna(val) else "" for val in row)
                        text_content.append(row_text)
                else:
                    text_content.append("(空工作表)")
                
                text_content.append("")  # 添加空行分隔
            
            result = "\n".join(text_content)
            logger.info(f"Excel文件 {file_name} 转换成功，内容长度: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"Excel文件转换失败: {str(e)}")
            raise ValueError(f"Excel文件转换失败: {str(e)}")
    
    @staticmethod
    def txt_to_text(file_content: bytes, encoding: str = "utf-8") -> str:
        """将TXT文件转换为文本"""
        try:
            # 尝试多种编码
            encodings = [encoding, "utf-8", "gbk", "gb2312", "latin-1"]
            
            for enc in encodings:
                try:
                    text = file_content.decode(enc)
                    logger.info(f"TXT文件使用编码 {enc} 转换成功，内容长度: {len(text)}")
                    return text
                except UnicodeDecodeError:
                    continue
            
            # 如果所有编码都失败，使用错误处理
            text = file_content.decode("utf-8", errors="replace")
            logger.warning("TXT文件转换时使用了错误替换")
            return text
            
        except Exception as e:
            logger.error(f"TXT文件转换失败: {str(e)}")
            raise ValueError(f"TXT文件转换失败: {str(e)}")
    
    @staticmethod
    def csv_to_text(file_content: bytes, encoding: str = "utf-8") -> str:
        """将CSV文件转换为文本"""
        try:
            # 先转换为文本
            text_content = TextConverter.txt_to_text(file_content, encoding)
            
            # 使用pandas读取CSV
            csv_file = io.StringIO(text_content)
            df = pd.read_csv(csv_file)
            
            # 转换为管道符分隔的格式
            text_lines = []
            
            # 添加表头
            header = "|".join(str(col) for col in df.columns)
            text_lines.append(header)
            
            # 添加数据行
            for _, row in df.iterrows():
                row_text = "|".join(str(val) if pd.notna(val) else "" for val in row)
                text_lines.append(row_text)
            
            result = "\n".join(text_lines)
            logger.info(f"CSV文件转换成功，内容长度: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"CSV文件转换失败: {str(e)}")
            raise ValueError(f"CSV文件转换失败: {str(e)}")
    
    @classmethod
    def convert_file(
        self,
        file_content: bytes,
        file_name: str,
        file_type: Optional[str] = None
    ) -> str:
        """根据文件类型转换文件"""
        # 从文件名推断文件类型
        if not file_type:
            file_extension = "." + file_name.split(".")[-1].lower()
            file_type = SUPPORTED_FILE_TYPES.get(file_extension)
        
        if not file_type:
            raise ValueError(f"不支持的文件类型: {file_name}")
        
        try:
            if file_type in [
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.ms-excel"
            ]:
                return TextConverter.excel_to_text(file_content, file_name)
            elif file_type == "text/plain":
                return TextConverter.txt_to_text(file_content)
            elif file_type == "text/csv":
                return TextConverter.csv_to_text(file_content)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            logger.error(f"文件转换失败: {file_name} - {str(e)}")
            raise
    
    @staticmethod
    def extract_table_info(text_content: str) -> Dict[str, Any]:
        """从文本中提取表格信息"""
        lines = text_content.strip().split("\n")
        
        table_info = {
            "has_table": False,
            "headers": [],
            "row_count": 0,
            "column_count": 0
        }
        
        for line in lines:
            if "|" in line:
                table_info["has_table"] = True
                columns = [col.strip() for col in line.split("|")]
                
                if not table_info["headers"]:
                    table_info["headers"] = columns
                    table_info["column_count"] = len(columns)
                else:
                    table_info["row_count"] += 1
        
        return table_info
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本内容"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\|\-\.\,\:\;\(\)\[\]\/\\]', '', text)
        return text.strip()


# 全局文本转换器实例
text_converter = TextConverter()
