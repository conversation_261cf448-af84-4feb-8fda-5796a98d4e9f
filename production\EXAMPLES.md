# 使用示例

## API调用示例

### 1. 通用能力示例

**请求:**
```bash
curl -X POST "http://localhost:8000/generate-config" \
     -H "Content-Type: application/json" \
     -d '{
       "user_input": "请配置华为路由器的BGP协议，AS号为65001",
       "device_type": "router",
       "vendor": "huawei",
       "stream": false
     }'
```

**响应:**
```json
{
  "success": true,
  "message": "配置生成成功",
  "code": 200,
  "config_content": "# BGP配置\nrouter bgp 65001\n...",
  "intent_type": "general",
  "device_type": "router",
  "vendor": "huawei"
}
```

### 2. 定制化能力示例

**请求:**
```bash
curl -X POST "http://localhost:8000/generate-config" \
     -H "Content-Type: application/json" \
     -d '{
       "user_input": "CE端BGP AS号|CE端设备|设备型号|CE端口|CE端互联IP地址|VLAN\n65001|Router1|AR2220|GE0/0/1|***********|100\n65002|Router2|AR2220|GE0/0/2|***********|200",
       "stream": false
     }'
```

**响应:**
```json
{
  "success": true,
  "message": "配置生成成功",
  "code": 200,
  "config_content": "#\n# 华为路由器配置模板\n# 设备类型: router\n# 厂商: huawei\n#\n\n#\n# 配置项 1 - Router1\n#\n\n# BGP AS号配置\nbgp 65001\n\n# 接口配置\ninterface GE0/0/1\n ip address *********** ***************\n undo shutdown\n\n# VLAN配置\nvlan 100\n description Auto generated VLAN\n\n#\n# 配置项 2 - Router2\n#\n\n# BGP AS号配置\nbgp 65002\n\n# 接口配置\ninterface GE0/0/2\n ip address *********** ***************\n undo shutdown\n\n# VLAN配置\nvlan 200\n description Auto generated VLAN\n\n#\n# 配置结束\n#",
  "intent_type": "customized",
  "device_type": "router",
  "vendor": "huawei"
}
```

### 3. 流式响应示例

**请求:**
```bash
curl -X POST "http://localhost:8000/generate-config" \
     -H "Content-Type: application/json" \
     -d '{
       "user_input": "请配置OSPF协议",
       "stream": true
     }'
```

**响应 (Server-Sent Events):**
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"general-capability","choices":[{"index":0,"delta":{"content":"# OSPF"},"finish_reason":null}]}

data: {"id":"chatcmpl-124","object":"chat.completion.chunk","created":1677652288,"model":"general-capability","choices":[{"index":0,"delta":{"content":"配置\n"},"finish_reason":null}]}

data: {"id":"chatcmpl-125","object":"chat.completion.chunk","created":1677652288,"model":"general-capability","choices":[{"index":0,"delta":{"content":"router ospf 1\n"},"finish_reason":null}]}

data: [DONE]
```

## Python客户端示例

```python
import httpx
import json
import asyncio

class ConfigAgentClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
    
    async def generate_config(self, user_input, device_type=None, vendor=None, stream=False):
        """生成配置"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/generate-config",
                json={
                    "user_input": user_input,
                    "device_type": device_type,
                    "vendor": vendor,
                    "stream": stream
                }
            )
            
            if stream:
                # 处理流式响应
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]
                        if data.strip() == "[DONE]":
                            break
                        try:
                            chunk = json.loads(data)
                            yield chunk
                        except json.JSONDecodeError:
                            continue
            else:
                return response.json()
    
    async def health_check(self):
        """健康检查"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/health")
            return response.json()

# 使用示例
async def main():
    client = ConfigAgentClient()
    
    # 健康检查
    health = await client.health_check()
    print(f"服务状态: {health}")
    
    # 生成配置（非流式）
    result = await client.generate_config(
        user_input="请配置华为交换机的VLAN",
        device_type="switch",
        vendor="huawei"
    )
    print(f"生成的配置:\n{result['config_content']}")
    
    # 生成配置（流式）
    print("\n流式生成配置:")
    async for chunk in client.generate_config(
        user_input="请配置BGP协议",
        stream=True
    ):
        if "choices" in chunk and chunk["choices"]:
            content = chunk["choices"][0]["delta"].get("content", "")
            print(content, end="", flush=True)

if __name__ == "__main__":
    asyncio.run(main())
```

## JavaScript客户端示例

```javascript
class ConfigAgentClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
    }
    
    async generateConfig(userInput, deviceType = null, vendor = null, stream = false) {
        const response = await fetch(`${this.baseUrl}/generate-config`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_input: userInput,
                device_type: deviceType,
                vendor: vendor,
                stream: stream
            })
        });
        
        if (stream) {
            return this.handleStreamResponse(response);
        } else {
            return await response.json();
        }
    }
    
    async *handleStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data.trim() === '[DONE]') {
                        return;
                    }
                    try {
                        yield JSON.parse(data);
                    } catch (e) {
                        // 忽略解析错误
                    }
                }
            }
        }
    }
    
    async healthCheck() {
        const response = await fetch(`${this.baseUrl}/health`);
        return await response.json();
    }
}

// 使用示例
async function main() {
    const client = new ConfigAgentClient();
    
    // 健康检查
    const health = await client.healthCheck();
    console.log('服务状态:', health);
    
    // 生成配置（非流式）
    const result = await client.generateConfig(
        '请配置华为路由器的静态路由',
        'router',
        'huawei'
    );
    console.log('生成的配置:', result.config_content);
    
    // 生成配置（流式）
    console.log('\n流式生成配置:');
    for await (const chunk of client.generateConfig(
        '请配置OSPF协议',
        null,
        null,
        true
    )) {
        if (chunk.choices && chunk.choices[0]) {
            const content = chunk.choices[0].delta.content || '';
            process.stdout.write(content);
        }
    }
}

main().catch(console.error);
```

## 模板自定义示例

### 创建自定义模板

**文件: templates/cisco_router.j2**
```jinja2
!
! Cisco路由器配置模板
! 生成时间: {{ generation_time }}
! 设备类型: {{ device_type }}
! 厂商: {{ vendor }}
!

{% for item in data_items %}
!
! 配置项 {{ loop.index }} - {{ item.get('设备名称', '设备' + loop.index|string) }}
!

{% if item.get('BGP AS号') %}
router bgp {{ item['BGP AS号'] }}
{% endif %}

{% if item.get('接口') and item.get('IP地址') %}
interface {{ item['接口'] }}
 ip address {{ item['IP地址'] }} {{ item.get('子网掩码', '*************') }}
 no shutdown
{% endif %}

{% if item.get('VLAN') %}
vlan {{ item['VLAN'] }}
 name {{ item.get('VLAN名称', 'VLAN_' + item['VLAN']) }}
{% endif %}

{% endfor %}

!
! 配置结束
!
```

### 使用自定义模板

当设备类型为"router"，厂商为"cisco"时，系统会自动使用`cisco_router.j2`模板。

## 错误处理示例

```python
import httpx
import asyncio

async def robust_config_generation():
    """健壮的配置生成示例"""
    client = httpx.AsyncClient(timeout=30.0)
    
    try:
        # 先检查服务状态
        health_response = await client.get("http://localhost:8000/health")
        if health_response.status_code != 200:
            print("服务不可用")
            return
        
        # 生成配置
        response = await client.post(
            "http://localhost:8000/generate-config",
            json={
                "user_input": "请配置BGP协议",
                "device_type": "router",
                "vendor": "huawei",
                "stream": False
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                print(f"配置生成成功:\n{result['config_content']}")
            else:
                print(f"配置生成失败: {result['message']}")
        else:
            print(f"请求失败: {response.status_code}")
            
    except httpx.TimeoutException:
        print("请求超时")
    except httpx.RequestError as e:
        print(f"网络错误: {e}")
    except Exception as e:
        print(f"未知错误: {e}")
    finally:
        await client.aclose()

if __name__ == "__main__":
    asyncio.run(robust_config_generation())
```

## 批量处理示例

```python
import asyncio
import httpx
from typing import List, Dict

async def batch_generate_configs(requests: List[Dict]) -> List[Dict]:
    """批量生成配置"""
    async with httpx.AsyncClient() as client:
        tasks = []
        
        for req in requests:
            task = client.post(
                "http://localhost:8000/generate-config",
                json=req
            )
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        results = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                results.append({
                    "index": i,
                    "success": False,
                    "error": str(response)
                })
            else:
                results.append({
                    "index": i,
                    "success": True,
                    "data": response.json()
                })
        
        return results

# 使用示例
async def main():
    requests = [
        {
            "user_input": "请配置BGP协议",
            "device_type": "router",
            "vendor": "huawei",
            "stream": False
        },
        {
            "user_input": "请配置VLAN",
            "device_type": "switch",
            "vendor": "cisco",
            "stream": False
        }
    ]
    
    results = await batch_generate_configs(requests)
    
    for result in results:
        if result["success"]:
            print(f"请求 {result['index']} 成功")
        else:
            print(f"请求 {result['index']} 失败: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
```
