"""
开放式能力模块
基于用户自定义Excel+TXT作为fewshot的配置生成
"""

from typing import Dict, Any, AsyncGenerator
from loguru import logger

from app.config.constants import PROMPT_TEMPLATES
from app.models.request import OpenCapabilityRequest
from app.utils.llm_client import llm_client


class OpenCapability:
    """开放式能力类"""
    
    def __init__(self):
        self.prompt_template = PROMPT_TEMPLATES["OPEN_CAPABILITY"]
    
    def _build_prompt(self, request: OpenCapabilityRequest) -> str:
        """构建提示词"""
        prompt = self.prompt_template.format(
            excel_content=request.example_excel,
            txt_content=request.example_txt,
            requirement_excel=request.requirement_excel,
            device_type=request.device_type,
            vendor=request.vendor
        )
        
        logger.info(f"构建开放式能力提示词，长度: {len(prompt)}")
        return prompt
    
    async def generate_config_stream(
        self, 
        request: OpenCapabilityRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）"""
        try:
            # 构建提示词
            prompt = self._build_prompt(request)
            
            # 调用LLM流式接口
            async for chunk in llm_client.chat_completion_stream(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3  # 降低温度以获得更稳定的输出
            ):
                yield chunk
            
            logger.info("开放式配置生成完成")
            
        except Exception as e:
            logger.error(f"开放式配置生成失败: {str(e)}")
            # 返回错误信息
            yield {
                "id": "chatcmpl-open-error",
                "object": "chat.completion.chunk",
                "created": 1677652288,
                "model": "open-capability",
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": f"配置生成失败: {str(e)}"},
                        "finish_reason": "stop"
                    }
                ]
            }
    
    async def generate_config(self, request: OpenCapabilityRequest) -> str:
        """生成配置（非流式返回）"""
        try:
            # 构建提示词
            prompt = self._build_prompt(request)
            
            # 调用LLM接口
            response = await llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                stream=False,
                temperature=0.3
            )
            
            # 提取生成的内容
            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                logger.info("开放式配置生成完成")
                return content
            else:
                raise ValueError("LLM响应格式不正确")
                
        except Exception as e:
            logger.error(f"开放式配置生成失败: {str(e)}")
            raise
    
    def _validate_request(self, request: OpenCapabilityRequest) -> bool:
        """验证请求参数"""
        if not request.example_excel.strip():
            raise ValueError("示例Excel内容不能为空")
        
        if not request.example_txt.strip():
            raise ValueError("示例TXT内容不能为空")
        
        if not request.requirement_excel.strip():
            raise ValueError("需求Excel内容不能为空")
        
        if not request.device_type.strip():
            raise ValueError("设备类型不能为空")
        
        if not request.vendor.strip():
            raise ValueError("厂商信息不能为空")
        
        return True
    
    async def generate_config_with_validation(
        self, 
        request: OpenCapabilityRequest,
        stream: bool = False
    ) -> str | AsyncGenerator[Dict[str, Any], None]:
        """带验证的配置生成"""
        # 验证请求参数
        self._validate_request(request)
        
        if stream:
            return self.generate_config_stream(request)
        else:
            return await self.generate_config(request)


# 全局开放式能力实例
open_capability = OpenCapability()
