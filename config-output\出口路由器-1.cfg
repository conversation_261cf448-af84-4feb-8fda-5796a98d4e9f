

! 设备: 出口路由器-1
! 出口路由器-1 配置开始

! 创建 VRF / VPN 实例
ip vpn-instance CDNA
 ipv4-family
  route-distinguisher 65159:242
  apply-label per-instance
  vpn-target 65159:242 export-extcommunity
  vpn-target 65159:242 import-extcommunity

! 与 防火墙（FW）互联的子接口
interface Eth-Trunk10.97
 vlan-type dot1q 97
 description To-FW-Eth-Trunk10
 ip binding vpn-instance CDNA
 ip address *********** ***************
 statistic enable

! 与 ER 互联的子接口
interface Eth-Trunk9.97
 vlan-type dot1q 97
 description To--Eth-Trunk9
 ip binding vpn-instance CDNA
 ip address ********* ***************
 statistic enable



! 路由前缀列表
! ——————————————————————

! 接收前缀列表
ip ip-prefix pl_CDNA_in index 10 permit *******/10 greater-equal 24 less-equal 32

! 发送前缀列表
ip ip-prefix pl_CDNA_out index 10 permit **********/24

! BGP 配置
bgp 65159
 ipv4-family vpn-instance CDNA
 peer ********* as-number 9929
 peer ********* description peer_to_A网 ER3
 peer ********* bfd enable
 peer ********* bfd min-tx-interval 150 min-rx-interval 150
 peer ********* ip-prefix pl_CDNA_in import
 peer ********* ip-prefix pl_CDNA_out export
 peer *********** as-number 65159
 peer *********** description peer_to_FW
 peer *********** bfd enable
 peer *********** bfd min-tx-interval 150 min-rx-interval 150
 peer *********** next-hop-local
 ! 出口路由器-1 配置结束