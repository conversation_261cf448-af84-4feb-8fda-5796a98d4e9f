"""
响应模型定义
"""

from typing import Optional, List, Any, Dict, Union
from pydantic import BaseModel, Field
from app.config.constants import IntentType, DeviceType, VendorType


class BaseResponse(BaseModel):
    """基础响应模型"""
    
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    code: int = Field(..., description="响应代码")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "请求成功",
                "code": 200
            }
        }


class ConfigGenerationResponse(BaseResponse):
    """配置生成响应模型"""
    
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    config_content: Optional[str] = Field(None, description="生成的配置内容")
    intent_type: Optional[str] = Field(None, description="识别的意图类型")
    device_type: Optional[str] = Field(None, description="设备类型")
    vendor: Optional[str] = Field(None, description="厂商名称")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "配置生成成功",
                "code": 200,
                "config_content": "interface GigabitEthernet0/0/1...",
                "intent_type": "general",
                "device_type": "router",
                "vendor": "huawei"
            }
        }


class IntentRecognitionResponse(BaseResponse):
    """意图识别响应模型"""
    
    intent_type: str = Field(..., description="识别的意图类型")
    confidence: float = Field(..., description="置信度")
    device_type: Optional[str] = Field(None, description="设备类型")
    vendor: Optional[str] = Field(None, description="厂商名称")
    extracted_info: Optional[Dict[str, Any]] = Field(None, description="提取的信息")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "意图识别成功",
                "code": 200,
                "intent_type": "customized",
                "confidence": 0.95,
                "device_type": "router",
                "vendor": "huawei",
                "extracted_info": {"table_headers": ["CE端BGP AS号", "CE端设备"]}
            }
        }


class FileConversionResponse(BaseResponse):
    """文件转换响应模型"""
    
    converted_content: str = Field(..., description="转换后的文本内容")
    file_type: str = Field(..., description="文件类型")
    original_name: str = Field(..., description="原始文件名")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "文件转换成功",
                "code": 200,
                "converted_content": "表格内容...",
                "file_type": "xlsx",
                "original_name": "config.xlsx"
            }
        }


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    
    error_type: str = Field(..., description="错误类型")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "message": "请求处理失败",
                "code": 400,
                "error_type": "ValidationError",
                "error_details": {"field": "user_input", "issue": "不能为空"}
            }
        }


class StreamResponse(BaseModel):
    """流式响应模型"""
    
    id: str = Field(..., description="响应ID")
    object: str = Field(default="chat.completion.chunk", description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="模型名称")
    choices: List[Dict[str, Any]] = Field(..., description="选择列表")
    
    class Config:
        schema_extra = {
            "example": {
                "id": "chatcmpl-123",
                "object": "chat.completion.chunk",
                "created": **********,
                "model": "Qwen3-235B-A22B",
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": "interface "},
                        "finish_reason": None
                    }
                ]
            }
        }


class HealthCheckResponse(BaseResponse):
    """健康检查响应模型"""
    
    version: str = Field(..., description="应用版本")
    uptime: float = Field(..., description="运行时间(秒)")
    status: str = Field(..., description="服务状态")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "服务正常",
                "code": 200,
                "version": "0.1.0",
                "uptime": 3600.5,
                "status": "healthy"
            }
        }
