"""
基础功能测试脚本
"""

import sys
import os
import asyncio

# 添加项目路径到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试基础导入"""
    try:
        print("测试基础导入...")
        
        # 测试配置模块
        from app.config.settings import settings
        from app.config.constants import IntentType
        print("✓ 配置模块导入成功")
        
        # 测试模型模块
        from app.models.request import ConfigGenerationRequest
        from app.models.response import ConfigGenerationResponse
        print("✓ 模型模块导入成功")
        
        # 测试工具模块
        from app.utils.text_converter import text_converter
        print("✓ 工具模块导入成功")
        
        # 测试服务模块
        from app.services.intent_recognition import intent_service
        print("✓ 服务模块导入成功")
        
        # 测试核心模块
        from app.core.customized_capability import customized_capability
        from app.core.open_capability import open_capability
        from app.core.general_capability import general_capability
        print("✓ 核心模块导入成功")
        
        print("所有模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 导入测试失败: {str(e)}")
        return False

def test_text_converter():
    """测试文本转换功能"""
    try:
        print("\n测试文本转换功能...")
        
        from app.utils.text_converter import text_converter
        
        # 测试Excel内容解析
        excel_content = """表头1|表头2|表头3
值1|值2|值3
值4|值5|值6"""
        
        table_info = text_converter.extract_table_info(excel_content)
        assert table_info["has_table"] is True
        assert len(table_info["headers"]) == 3
        print("✓ 文本转换功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 文本转换测试失败: {str(e)}")
        return False

async def test_intent_recognition():
    """测试意图识别功能"""
    try:
        print("\n测试意图识别功能...")
        
        from app.services.intent_recognition import intent_service
        from app.models.request import IntentRecognitionRequest
        
        # 测试通用意图
        request = IntentRecognitionRequest(
            user_input="请配置华为路由器的BGP协议"
        )
        
        result = await intent_service.recognize_intent(request)
        assert result.success is True
        print(f"✓ 意图识别结果: {result.intent_type}")
        
        # 测试定制化意图
        request2 = IntentRecognitionRequest(
            user_input="CE端BGP AS号|CE端设备|设备型号|CE端口"
        )
        
        result2 = await intent_service.recognize_intent(request2)
        assert result2.success is True
        print(f"✓ 定制化意图识别结果: {result2.intent_type}")
        
        print("✓ 意图识别功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 意图识别测试失败: {str(e)}")
        return False

async def test_customized_capability():
    """测试定制化能力"""
    try:
        print("\n测试定制化能力...")
        
        from app.core.customized_capability import customized_capability
        from app.models.request import CustomizedCapabilityRequest
        
        request = CustomizedCapabilityRequest(
            excel_content="""CE端BGP AS号|CE端设备|设备型号
65001|Router1|AR2220
65002|Router2|AR2220""",
            device_type="router",
            vendor="huawei"
        )
        
        # 测试解析功能
        data_items = customized_capability._parse_excel_content(request.excel_content)
        assert len(data_items) == 2
        assert data_items[0]["CE端BGP AS号"] == "65001"
        
        print("✓ 定制化能力测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 定制化能力测试失败: {str(e)}")
        return False

def test_settings():
    """测试配置"""
    try:
        print("\n测试配置...")
        
        from app.config.settings import settings
        
        print(f"应用名称: {settings.app_name}")
        print(f"应用版本: {settings.app_version}")
        print(f"调试模式: {settings.debug}")
        print(f"服务器地址: {settings.host}:{settings.port}")
        
        print("✓ 配置测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("开始基础功能测试...\n")
    
    tests = [
        ("导入测试", test_imports),
        ("配置测试", test_settings),
        ("文本转换测试", test_text_converter),
        ("意图识别测试", test_intent_recognition),
        ("定制化能力测试", test_customized_capability),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}执行异常: {str(e)}")
    
    print(f"\n测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目基础功能正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关模块。")
        return False

if __name__ == "__main__":
    asyncio.run(main())
