version: '3.8'

services:
  config-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: config-agent-py
    ports:
      - "8000:8000"
    environment:
      - APP_NAME=数通设备配置生成智能体
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - WORKERS=4
      - LLM_BASE_URL=http://************:10010/CUCCAI-llm-hub/chat/completions
      - LLM_API_KEY=618149eb-d43e-4ddc-b406-b0c0e1efd281
      - LLM_MODEL=Qwen3-235B-A22B
      - KB_BASE_URL=http://************:10010/CUCCAI-intelligent-agent/vectorSearchApi/
      - KB_APP_ID=urdDUFiZhKrZi
      - KB_CATEGORY_ID=153
    volumes:
      - ./logs:/app/logs
      - ./templates:/app/templates
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - config-agent-network

  # 可选：添加Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: config-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - config-agent-network
    command: redis-server --appendonly yes

  # 可选：添加Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: config-agent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - config-agent
    restart: unless-stopped
    networks:
      - config-agent-network

volumes:
  redis_data:

networks:
  config-agent-network:
    driver: bridge
