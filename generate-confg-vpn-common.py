import pandas as pd
from jinja2 import Environment, FileSystemLoader
import os
import ipaddress
import openpyxl

template_path="template/config-vpn-common.j2"
output_dir="config-output"
input_excel_path="input_source/config.xlsx"



def cidr_to_ip_mask(ip_cidr):
    ip_interface = ipaddress.IPv4Interface(ip_cidr)
    return str(ip_interface.ip), str(ip_interface.netmask)

def generate_configs():
    # 加载 Jinja2 模板环境
    env = Environment(loader=FileSystemLoader('.'))
    template = env.get_template(template_path)  # 修改为你最新的模板文件名
    # 读取 Excel 表格
    df = pd.read_excel(input_excel_path, dtype=str)
    # 输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    for index, row in df.iterrows():
        # 跳过空行
        if row.isnull().all():
            continue
        # 解析字段
        info_notinside_excel={
            'fw_ip_cidr':'***********/30',
            'fw_interface':'Eth-Trunk10'
        }
        ce_name = row['CE端设备'].strip()
        ce_port = row['CE端口'].strip()
        ce_ip_cidr = row['CE端互联IP地址'].strip()
        vlan = row['VLAN'].strip()
        bfd_str = row['BFD时延'].strip()
        bfd_interval = ''.join(filter(str.isdigit, bfd_str))  # 提取数字部分
        vpn_instance = row['vpn-instance'].strip()
        rd_rt = row['rt/rd'].strip()
        in_prefix = row['接收地址范围'].strip().replace('\n', ' ')
        in_prefix = in_prefix.split('最小接受')[0].strip()  # 只取前缀部分
        out_prefix = row['发送地址范围'].strip().split('\n')[0].strip()
        bgp_as = row['CE端BGP AS号'].strip()
        er_bgp_as = row['AS号'].strip() if not pd.isna(row['AS号']) else ''
        er_name = row['终端设备'].strip()
        er_interface = row['终端端口'].strip() if not pd.isna(row['终端端口']) else ''
        er_ip_cidr = row['ER互联IP地址'].strip() if not pd.isna(row['ER互联IP地址']) else ''
        fw_ip_cidr = row.get('防火墙互联IP地址', info_notinside_excel['fw_ip_cidr']).strip()  # 可选，支持用户自定义
        fw_bgp_as = bgp_as  # 假设与 CE 的 AS 相同
        fw_name = "FW"
        # 解析 IP 地址
        er_ip, er_mask = cidr_to_ip_mask(ce_ip_cidr)
        fw_ip, fw_mask = cidr_to_ip_mask(fw_ip_cidr)
        neighbor_er_ip = er_ip_cidr.split('/')[0] if '/' in er_ip_cidr else er_ip_cidr.split('/')[0]
        # 构建 BGP 邻居列表
        bgp_peers = []
        # 添加 ER 邻居
        if neighbor_er_ip and er_bgp_as:
            bgp_peers.append({
                "ip": neighbor_er_ip,
                "as_number": er_bgp_as,
                "description": f"peer_to_{er_name}",
                "import_filter": f"pl_{vpn_instance}_in",
                "export_filter": f"pl_{vpn_instance}_out"
            })
        # 添加 FW 邻居（通常使用本设备 CE 的 AS）
        fw_neighbor_ip = fw_ip_cidr.split('/')[0]
        bgp_peers.append({
            "ip": fw_neighbor_ip,
            "as_number": fw_bgp_as,
            "description": f"peer_to_{fw_name}",
            "next_hop_local": True
        })
        # 渲染模板
        output = template.render(
            bgp_as=bgp_as,
            vpn_instance=vpn_instance,
            rd_rt=rd_rt,
            bfd_interval=bfd_interval,
            bgp_peers=bgp_peers,
            ce_name=ce_name,
            ce_interface=ce_port,
            vlan=vlan,
            er_ip=er_ip,
            er_mask=er_mask,
            fw_ip=fw_ip,
            fw_mask=fw_mask,
            in_prefix=in_prefix,
            out_prefix=out_prefix,
            er_interface=ce_port,
            fw_interface=info_notinside_excel['fw_interface']  # 示例接口名，你可以用 Excel 字段替代
        )
        # 保存配置文件
        filename = f"{output_dir}/{ce_name.replace(' ', '_').strip()}.cfg"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(output)
        print(f"✅ 配置文件 {filename} 已生成")
if __name__ == '__main__':
    generate_configs()