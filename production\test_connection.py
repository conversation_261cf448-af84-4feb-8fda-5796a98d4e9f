"""
网络连接测试脚本
"""

import sys
import os
import asyncio
import httpx
import json

# 添加项目路径到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.config.settings import settings

async def test_basic_connection():
    """测试基础网络连接"""
    print("=== 基础网络连接测试 ===")
    
    url = settings.llm_base_url
    api_key = settings.llm_api_key
    model = settings.llm_model
    
    print(f"目标URL: {url}")
    print(f"API密钥: {api_key[:20]}...")
    print(f"模型: {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "Hello, this is a test message."
            }
        ],
        "stream": False,
        "temperature": 0.1,
        "max_tokens": 50
    }
    
    try:
        print("\n1. 测试基础连接...")
        async with httpx.AsyncClient(
            timeout=httpx.Timeout(30.0, connect=10.0),
            verify=False
        ) as client:
            print("   - 创建HTTP客户端成功")
            
            print("   - 发送POST请求...")
            response = await client.post(
                url,
                headers=headers,
                json=payload
            )
            
            print(f"   - 响应状态码: {response.status_code}")
            print(f"   - 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   - 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                print("✅ 连接测试成功！")
                return True
            else:
                print(f"   - 响应文本: {response.text}")
                print("❌ 连接测试失败：状态码不是200")
                return False
                
    except httpx.ConnectError as e:
        print(f"❌ 连接错误: {str(e)}")
        return False
    except httpx.TimeoutException as e:
        print(f"❌ 超时错误: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {type(e).__name__} - {str(e)}")
        return False

async def test_llm_client():
    """测试LLM客户端"""
    print("\n=== LLM客户端测试 ===")
    
    try:
        from app.utils.llm_client import llm_client
        
        print("1. 测试LLM客户端初始化...")
        print(f"   - 基础URL: {llm_client.base_url}")
        print(f"   - 模型: {llm_client.model}")
        print(f"   - 超时: {llm_client.timeout}")
        
        print("2. 测试聊天完成接口...")
        messages = [
            {
                "role": "user",
                "content": "请回答：1+1等于几？"
            }
        ]
        
        result = await llm_client.chat_completion(
            messages=messages,
            stream=False,
            temperature=0.1
        )
        
        print("✅ LLM客户端测试成功！")
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return True
        
    except Exception as e:
        print(f"❌ LLM客户端测试失败: {type(e).__name__} - {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_dns_resolution():
    """测试DNS解析"""
    print("\n=== DNS解析测试 ===")
    
    import socket
    
    try:
        hostname = "api.siliconflow.cn"
        print(f"解析主机名: {hostname}")
        
        ip_address = socket.gethostbyname(hostname)
        print(f"IP地址: {ip_address}")
        
        print("✅ DNS解析成功！")
        return True
        
    except Exception as e:
        print(f"❌ DNS解析失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("开始网络连接诊断...\n")
    
    tests = [
        ("DNS解析测试", test_dns_resolution),
        ("基础网络连接测试", test_basic_connection),
        ("LLM客户端测试", test_llm_client),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"执行: {test_name}")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"测试完成: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有网络测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查网络配置。")
        return False

if __name__ == "__main__":
    asyncio.run(main())
