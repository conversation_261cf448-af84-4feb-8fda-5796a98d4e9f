# 数通设备配置生成智能体服务

## 项目简介

数通设备配置生成智能体是一个基于FastAPI的RESTful服务，专门用于自动化生成数通设备配置。该服务集成了意图识别、模板引擎、大语言模型和知识库检索等技术，为用户提供三种不同的配置生成能力。

## 技术栈

### 核心框架
- **Python 3.12+**: 主要编程语言
- **FastAPI**: 现代、高性能的Web框架
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证和序列化

### 依赖库
- **httpx**: 异步HTTP客户端
- **jinja2**: 模板引擎
- **pandas**: 数据处理
- **openpyxl**: Excel文件处理
- **loguru**: 日志管理
- **tenacity**: 重试机制
- **python-dotenv**: 环境变量管理

### 开发工具
- **uv**: 包管理和虚拟环境
- **pytest**: 测试框架
- **black**: 代码格式化
- **isort**: 导入排序
- **mypy**: 类型检查

## 业务逻辑

### 核心功能

1. **意图识别模块**
   - 自动识别用户输入的意图类型
   - 提取设备类型和厂商信息
   - 支持三种意图：定制化、开放式、通用

2. **三项核心能力**
   - **定制化能力**: 基于预定义Excel表头和Jinja2模板生成配置
   - **开放式能力**: 基于用户提供的示例Excel+TXT作为few-shot学习生成配置
   - **通用能力**: 基于RAG（检索增强生成）技术生成配置

3. **文本转换模块**
   - 支持Excel、TXT、CSV文件转换为文本格式
   - 统一的文本处理接口

### 业务流程

```
用户输入 → 意图识别 → 核心能力路由 → 配置生成 → 流式返回
```

## 架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI       │    │   意图识别       │    │   核心能力       │
│   主服务        │───▶│   服务          │───▶│   模块          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   中间件        │    │   文本转换       │    │   LLM客户端     │
│   异常处理      │    │   工具          │    │   知识库客户端   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 模块设计

```
production/
├── app/                    # 应用主目录
│   ├── config/            # 配置模块
│   │   ├── settings.py    # 应用配置
│   │   └── constants.py   # 常量定义
│   ├── core/              # 核心能力模块
│   │   ├── customized_capability.py   # 定制化能力
│   │   ├── open_capability.py         # 开放式能力
│   │   └── general_capability.py      # 通用能力
│   ├── services/          # 服务层
│   │   ├── intent_recognition.py      # 意图识别
│   │   └── config_generation.py       # 配置生成服务
│   ├── utils/             # 工具模块
│   │   ├── llm_client.py             # LLM客户端
│   │   ├── knowledge_base.py         # 知识库客户端
│   │   ├── text_converter.py         # 文本转换
│   │   └── logger.py                 # 日志配置
│   ├── models/            # 数据模型
│   │   ├── request.py     # 请求模型
│   │   └── response.py    # 响应模型
│   └── main.py           # 主应用入口
├── templates/            # Jinja2模板
├── tests/               # 测试用例
├── logs/                # 日志文件
└── requirements.txt     # 依赖列表
```

## 部署设计

### Docker部署

1. **单容器部署**
```bash
# 构建镜像
docker build -t config-agent-py .

# 运行容器
docker run -p 8000:8000 config-agent-py
```

2. **Docker Compose部署**
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f config-agent
```

### 生产环境部署

1. **环境准备**
```bash
# 安装uv
pip install uv

# 创建虚拟环境
uv venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows

# 安装依赖
uv sync
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置
vim .env
```

3. **启动服务**
```bash
# 开发模式
uv run python start.py

# 生产模式
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 并发设计

### 异步处理
- 全面采用async/await异步编程模式
- 使用httpx异步HTTP客户端
- 支持流式响应处理

### 并发控制
- 配置最大并发请求数限制
- 实现请求速率限制
- 使用连接池管理外部服务连接

### 性能优化
- 启用Gzip压缩
- 实现请求缓存机制
- 优化数据库查询和外部API调用

## API接口

### 主要接口

1. **配置生成接口**
```
POST /generate-config
Content-Type: application/json

{
    "user_input": "用户输入文本",
    "device_type": "设备类型（可选）",
    "vendor": "厂商名称（可选）",
    "stream": true/false
}
```

2. **健康检查接口**
```
GET /health
```

### 响应格式

- **非流式响应**: 标准JSON格式
- **流式响应**: Server-Sent Events (SSE) 格式，兼容OpenAI标准

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| APP_NAME | 应用名称 | 数通设备配置生成智能体 |
| DEBUG | 调试模式 | false |
| HOST | 服务器主机 | 0.0.0.0 |
| PORT | 服务器端口 | 8000 |
| LLM_BASE_URL | LLM服务地址 | - |
| LLM_API_KEY | LLM API密钥 | - |
| KB_BASE_URL | 知识库服务地址 | - |

### 模板配置

支持按设备类型和厂商自定义Jinja2模板：
- `{vendor}_{device_type}.j2`: 特定厂商设备模板
- `generic_{device_type}.j2`: 通用设备类型模板
- `default.j2`: 默认模板

## 测试

### 运行测试
```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest tests/test_intent_recognition.py

# 生成覆盖率报告
uv run pytest --cov=app --cov-report=html
```

### 测试覆盖
- 单元测试：各模块功能测试
- 集成测试：API接口测试
- 模拟测试：外部服务模拟

## 监控和日志

### 日志配置
- 控制台日志：彩色格式，支持不同级别
- 文件日志：按日期轮转，自动压缩
- 错误日志：单独记录错误信息

### 健康检查
- 服务状态监控
- 运行时间统计
- 版本信息展示

## 安全考虑

### 输入验证
- 使用Pydantic进行数据验证
- 限制文件上传大小
- 防止SQL注入和XSS攻击

### 访问控制
- CORS配置
- 请求速率限制
- API密钥验证

### 数据保护
- 敏感信息脱敏
- 日志信息过滤
- 安全头部设置

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口占用
   - 验证环境变量配置
   - 查看日志文件

2. **LLM调用失败**
   - 检查网络连接
   - 验证API密钥
   - 查看超时设置

3. **模板渲染错误**
   - 检查模板语法
   - 验证数据格式
   - 查看错误日志

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看Docker日志
docker-compose logs -f config-agent
```

## 开发指南

### 代码规范
- 遵循PEP 8编码规范
- 使用类型注解
- 编写详细的文档字符串

### 提交规范
- 使用语义化提交信息
- 运行代码格式化工具
- 确保测试通过

### 扩展开发
- 新增核心能力模块
- 自定义模板开发
- 集成新的外部服务

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
